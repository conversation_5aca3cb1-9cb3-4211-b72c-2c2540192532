from fastapi import APIRouter
from fastapi.responses import StreamingResponse

from openai import OpenAI
import pandas as pd

router = APIRouter()

# for backward compatibility, you can still use `https://api.deepseek.com/v1` as `base_url`.
client = OpenAI(api_key="sk-e6799f3c8d274ca281a1673fc04229f5", base_url="https://api.deepseek.com")

# 读取文件
excel_file = pd.ExcelFile('罗曼粉V1.xlsx')

# 获取指定工作表中的数据
df = excel_file.parse('Sheet1')

# 计算相关时间点
start_age = 64
end_age = start_age + 48 * 7 - 1

# 筛选出项目开始（64 日龄）到项目结束（64 日龄开始的 48 周）的数据
project_data = df[(df['日龄'] >= start_age) & (df['日龄'] <= end_age)].copy()

# 将 DataFrame 转换为数组
data_array = project_data['饲料日采购量(吨)'].tolist()

query = f"""

1. 基础参数设定
1.1 项目基准日：本项目以 2023 年 1 月 1 日作为起始日期，后续所有的时间计算和数据统计都将以此为基准。
1.2 年化利率：设定年化利率为 10%，并且在计算利息时，一年按照 360 天来进行计算，方便统一资金成本的计算标准。
1.3 全价料价格
1.3.1 全价料的销售价格为每吨 3000 元，这是在销售饲料时的定价依据。
1.3.2 全价料的采购价格是每吨 2900 元，这是采购饲料所需要支付的成本价格。
1.4 税率与费率
1.4.1 印花税率为 0.03%，在涉及销售和采购业务时，需要按照此税率计算印花税。
1.4.2 企业所得税率设定为 25%，用于计算企业在经营过程中需要缴纳的所得税。
1.4.3 管理费率为 0.75%，这是用于计算企业在运营过程中产生的管理成本的比例。

2. 项目周期设定
2.1 项目周期：2023 年 1 月 1 日是蛋鸡 64 日龄，持续时间为 48 周，换算成天数总共是 336 天。在这个周期内，会进行饲料的采购和销售等一系列业务活动。
2.2 投入期：项目的前 12 周，也就是 84 天，被设定为集中采购期。在这个阶段，会集中进行饲料的采购工作，以满足后续项目的需求。

3. 核心数据计算
3.1 总采购量计算：从数据中筛选出蛋鸡在 64 日龄至 399 日龄（即项目周期内）每天的饲料采购数据，然后将这些每天的采购量进行累加，最终得到整个项目周期内的总采购吨数。
3.2 基础收支计算
3.2.1 销售金额：用前面计算得出的总采购吨数乘以全价料的销售单价（每吨 3000 元），就可以得到整个项目的销售金额。
3.2.2 采购支出：同样，将总采购吨数乘以全价料的采购单价（每吨 2900 元），从而得出整个项目的采购支出。
3.3 资金成本计算
3.3.1 投入期采购资金成本
3.3.1.1 先将投入期（前 12 周）的采购数据按日龄减去 64 后再除以 7 取整进行分组汇总，计算出每周的采购量。
3.3.1.2 对于每一笔采购，假设其账期为 90 天，按照 “采购金额 × 利率 × 90÷360” 的公式来计算每笔采购所产生的利息成本。最后将每周的利息成本相加，得到投入期采购资金的总成本。
3.3.2 赊销款资金成本
3.3.2.1 把投入期（前 12 周）的总的销售金额确定为赊销款金额。这部分赊销款将按照 9 期进行分期返还，每期的返还比例分别为 10%、10%、20%、20%、20%、5%、5%、5%、5%。
3.3.2.2 打印每笔赊销款的信息，包括赊销款日期（从项目开始日期起，按每 7 天）、每期采购吨数、赊销金额（销售价 × 采购吨数）、天数（从赊销日到项目结束日的天数）和资金成本（赊销金额 × 年化利率 × 天数 / 360）。
3.3.2.3 投入期结束日期=项目开始日期 + 投入期天数
3.3.2.4 首次还款日期为投入期结束日期的次月 5 日（投入期结束日期的月份加 1 并确保是5日），后续还款日期在此基础上的次月 5 日（月份加 1 并确保是5日）。
3.3.2.5 打印每期还款的信息，包括还款日期、还款金额、天数（从还款日到项目结束日的天数）和资金成本（还款金额 × 年化利率 × 天数 / 360）。
3.3.2.6 计算赊销款总资金成本（每笔赊销款资金成本之和）和还款总资金成本（每期还款资金成本之和），用赊销款总资金成本减去还款总资金成本，得到赊销款的净资金成本。

4. 税费计算
4.1 印花税：将销售额和采购成本（采购支出 + 投入期采购资金成本）相加，然后乘以印花税率（0.03%），从而计算出需要缴纳的印花税金额。
4.2 管理成本：用销售额乘以管理费率（0.75%），得到企业在项目运营过程中产生的管理成本。
4.3 企业所得税：用销售额减去采购支出、投入期采购资金成本、印花税和管理成本，得到应纳税所得额。最后将应纳税所得额乘以企业所得税率（25%），得出企业需要缴纳的所得税金额。
最终毛利率计算：用销售额减去采购支出、赊销款资金成本、印花税、企业所得税以及管理成本，得到的差值再除以销售额，最后乘以 100%，就得到了项目的最终毛利率。这个毛利率反映了项目在扣除各项成本和税费后的盈利水平。

以下是蛋鸡日龄64至399的每日的饲料采购吨数：
{data_array}

根据上述描述计算：
采购吨数,
销售金额,
采购成本,
采购支出,
投入期采购支出资金成本,
养殖户赊销款的资金成本,
印花税,
企业所得税,
管理成本,
毛利率(%)
"""

@router.get("/stream")
async def chat_stream():
    async def generate():
        stream = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system",
                 "content": "你是一个能够根据给定项目参数和计算逻辑，准确计算项目毛利的计算助手，数据格式要求为财务数据格式，如：#,###,###.##"},
                {"role": "user", "content": query},
            ],
            max_tokens=8192,
            temperature=0.7,
            stream=True
        )
        for chunk in stream:
            if not chunk.choices:
                continue
            print(chunk.choices[0].delta.content, end="")
            yield f"data: {chunk}\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/event-stream"
    )